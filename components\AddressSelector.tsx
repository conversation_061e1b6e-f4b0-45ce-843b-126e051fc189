import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Modal,
  ScrollView,
  TextInput,
  Alert,
} from "react-native";
import { Home, MapPin, Plus, Edit3, Trash2, Check } from "lucide-react-native";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import Button from "@/components/Button";
import { useAddressStore, Address } from "@/stores/useAddressStore";
import { useTranslation } from "@/hooks/useTranslation";

interface AddressSelectorProps {
  selectedAddress: Address | null;
  onAddressSelect: (address: Address) => void;
}

export default function AddressSelector({ selectedAddress, onAddressSelect }: AddressSelectorProps) {
  const { t } = useTranslation();
  const { addresses, addAddress, deleteAddress, setDefaultAddress } = useAddressStore();
  const [showModal, setShowModal] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newAddress, setNewAddress] = useState({
    label: "",
    address: "",
    type: "other" as "home" | "work" | "other",
    isDefault: false,
  });

  const getAddressIcon = (type: string) => {
    switch (type) {
      case "home":
        return <Home size={20} color={colors.primary} />;
      case "work":
        return <MapPin size={20} color={colors.primary} />;
      default:
        return <MapPin size={20} color={colors.primary} />;
    }
  };

  const handleAddAddress = () => {
    if (!newAddress.label.trim() || !newAddress.address.trim()) {
      Alert.alert("Error", "Please fill in all fields");
      return;
    }

    addAddress(newAddress);
    setNewAddress({
      label: "",
      address: "",
      type: "other",
      isDefault: false,
    });
    setShowAddForm(false);
  };

  const handleDeleteAddress = (address: Address) => {
    Alert.alert(
      "Delete Address",
      `Are you sure you want to delete "${address.label}"?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => deleteAddress(address.id),
        },
      ]
    );
  };

  const handleSelectAddress = (address: Address) => {
    onAddressSelect(address);
    setShowModal(false);
  };

  return (
    <>
      <TouchableOpacity style={styles.selector} onPress={() => setShowModal(true)}>
        <View style={styles.selectorContent}>
          <MapPin size={20} color={colors.primary} />
          <View style={styles.selectorText}>
            <Text style={styles.selectorLabel}>{t("serviceLocation")}</Text>
            <Text style={styles.selectorValue}>
              {selectedAddress ? selectedAddress.label : t("selectAddress")}
            </Text>
          </View>
        </View>
        <Text style={styles.changeText}>{t("change")}</Text>
      </TouchableOpacity>

      <Modal visible={showModal} animationType="slide" presentationStyle="pageSheet">
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{t("selectAddress")}</Text>
            <TouchableOpacity onPress={() => setShowModal(false)}>
              <Text style={styles.cancelText}>{t("cancel")}</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {addresses.map((address) => (
              <TouchableOpacity
                key={address.id}
                style={[
                  styles.addressItem,
                  selectedAddress?.id === address.id && styles.selectedAddressItem,
                ]}
                onPress={() => handleSelectAddress(address)}
              >
                <View style={styles.addressContent}>
                  <View style={styles.addressHeader}>
                    {getAddressIcon(address.type)}
                    <Text style={styles.addressLabel}>{address.label}</Text>
                    {address.isDefault && (
                      <View style={styles.defaultBadge}>
                        <Text style={styles.defaultText}>{t("default")}</Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.addressText}>{address.address}</Text>
                </View>
                {selectedAddress?.id === address.id && (
                  <Check size={20} color={colors.primary} />
                )}
              </TouchableOpacity>
            ))}

            <TouchableOpacity
              style={styles.addAddressButton}
              onPress={() => setShowAddForm(true)}
            >
              <Plus size={20} color={colors.primary} />
              <Text style={styles.addAddressText}>{t("addNewAddress")}</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </Modal>

      <Modal visible={showAddForm} animationType="slide" presentationStyle="pageSheet">
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{t("addNewAddress")}</Text>
            <TouchableOpacity onPress={() => setShowAddForm(false)}>
              <Text style={styles.cancelText}>{t("cancel")}</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.formContainer}>
              <View style={styles.inputContainer}>
                <Text style={styles.label}>{t("addressLabel")}</Text>
                <TextInput
                  style={styles.input}
                  placeholder={t("addressLabelPlaceholder")}
                  value={newAddress.label}
                  onChangeText={(text) => setNewAddress({ ...newAddress, label: text })}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>{t("fullAddress")}</Text>
                <TextInput
                  style={[styles.input, styles.addressInput]}
                  placeholder={t("fullAddressPlaceholder")}
                  value={newAddress.address}
                  onChangeText={(text) => setNewAddress({ ...newAddress, address: text })}
                  multiline
                  numberOfLines={3}
                />
              </View>

              <View style={styles.typeContainer}>
                <Text style={styles.label}>{t("addressType")}</Text>
                <View style={styles.typeButtons}>
                  {["home", "work", "other"].map((type) => (
                    <TouchableOpacity
                      key={type}
                      style={[
                        styles.typeButton,
                        newAddress.type === type && styles.selectedTypeButton,
                      ]}
                      onPress={() => setNewAddress({ ...newAddress, type: type as any })}
                    >
                      <Text
                        style={[
                          styles.typeButtonText,
                          newAddress.type === type && styles.selectedTypeButtonText,
                        ]}
                      >
                        {t(`addressType.${type}`)}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              <Button
                title={t("addAddress")}
                onPress={handleAddAddress}
                style={styles.addButton}
                fullWidth
              />
            </View>
          </ScrollView>
        </View>
      </Modal>
    </>
  );
}

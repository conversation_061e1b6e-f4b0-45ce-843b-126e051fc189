import React, { useState } from "react";
import { StyleSheet, Text, View, ScrollView, Switch, TouchableOpacity } from "react-native";
import { Stack } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { ChevronRight, Lock, Eye, Scale } from "lucide-react-native";
import Button from "@/components/Button";
import { useTranslation } from "@/hooks/useTranslation";

export default function PrivacySecurityScreen() {
  const { t } = useTranslation();
  const [locationTracking, setLocationTracking] = useState(true);
  const [dataCollection, setDataCollection] = useState(true);

  return (
    <>
      <Stack.Screen options={{ title: t("privacySecurity") }} />
      
      <ScrollView style={styles.container}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t("security")}</Text>
          
          <TouchableOpacity style={styles.settingItem}>
            <Text style={styles.settingText}>{t("changePassword")}</Text>
            <ChevronRight size={20} color={colors.textSecondary} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.settingItem}>
            <Text style={styles.settingText}>{t("twoFactorAuth")}</Text>
            <ChevronRight size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t("privacy")}</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <Text style={styles.settingText}>{t("locationTracking")}</Text>
              <Text style={styles.settingDescription}>
                {t("locationTrackingDescription")}
              </Text>
            </View>
            <Switch
              value={locationTracking}
              onValueChange={setLocationTracking}
              trackColor={{ false: colors.border, true: colors.primaryLight }}
              thumbColor={locationTracking ? colors.primary : colors.card}
            />
          </View>
          
          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <Text style={styles.settingText}>{t("dataCollection")}</Text>
              <Text style={styles.settingDescription}>
                {t("dataCollectionDescription")}
              </Text>
            </View>
            <Switch
              value={dataCollection}
              onValueChange={setDataCollection}
              trackColor={{ false: colors.border, true: colors.primaryLight }}
              thumbColor={dataCollection ? colors.primary : colors.card}
            />
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t("legal")}</Text>
          
          <TouchableOpacity style={styles.settingItem}>
            <Text style={styles.settingText}>{t("privacyPolicy")}</Text>
            <ChevronRight size={20} color={colors.textSecondary} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.settingItem}>
            <Text style={styles.settingText}>{t("termsOfService")}</Text>
            <ChevronRight size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
        
        <Button
          title={t("deleteAccount")}
          onPress={() => {
            // Show confirmation dialog
            alert(t("accountDeletionWarning"));
          }}
          variant="outline"
          style={styles.deleteButton}
          textStyle={{ color: colors.error }}
        />
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    ...typography.h4,
    paddingHorizontal: 16,
    paddingVertical: 12,
    color: colors.textSecondary,
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  settingText: {
    ...typography.body,
  },
  settingLeft: {
    flex: 1,
    marginRight: 16,
  },
  settingDescription: {
    ...typography.bodySmall,
    color: colors.textSecondary,
    marginTop: 4,
  },
  deleteButton: {
    margin: 16,
    borderColor: colors.error,
    marginBottom: 32,
  },
});
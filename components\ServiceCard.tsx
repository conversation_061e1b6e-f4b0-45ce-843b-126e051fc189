import React from "react";
import { StyleSheet, Text, View, TouchableOpacity } from "react-native";
import { Service } from "@/mocks/providers";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { Clock } from "lucide-react-native";
import { useTranslation } from "@/hooks/useTranslation";

interface ServiceCardProps {
  service: Service;
  onPress: (service: Service) => void;
}

export default function ServiceCard({ service, onPress }: ServiceCardProps) {
  const { t } = useTranslation();
  
  // Format duration to hours and minutes
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return `${hours}${t("hour")}${mins > 0 ? ` ${mins}${t("minute")}` : ""}`;
    }
    return `${mins}${t("minute")}`;
  };

  return (
    <TouchableOpacity
      style={styles.card}
      onPress={() => onPress(service)}
      activeOpacity={0.7}
    >
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.name}>{service.name || t(service.nameKey)}</Text>
          <Text style={styles.price}>R{service.price}</Text>
        </View>
        
        <Text style={styles.description} numberOfLines={2}>
          {service.description || t(service.descriptionKey)}
        </Text>
        
        <View style={styles.durationContainer}>
          <Clock size={14} color={colors.textSecondary} strokeWidth={2} />
          <Text style={styles.duration}>{formatDuration(service.duration)}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.primaryLight,
    borderRadius: 12,
    marginBottom: 12,
    overflow: "hidden",
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  name: {
    ...typography.h4,
    flex: 1,
    marginRight: 8,
    color: colors.primary,
  },
  price: {
    ...typography.h3,
    color: colors.primary,
  },
  description: {
    ...typography.bodySmall,
    marginBottom: 12,
  },
  durationContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  duration: {
    ...typography.caption,
    marginLeft: 4,
  },
});
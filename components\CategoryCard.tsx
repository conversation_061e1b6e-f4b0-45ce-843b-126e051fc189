import React from "react";
import { StyleSheet, Text, View, TouchableOpacity } from "react-native";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import {
  Home,
  Scissors,
  Briefcase,
  GraduationCap,
  Calendar,
  Car,
  Truck,
  Wrench
} from "lucide-react-native";
import { useTranslation } from "@/hooks/useTranslation";
import { ServiceCategory } from "@/mocks/categories";

interface CategoryCardProps {
  category: ServiceCategory;
  onPress: (category: ServiceCategory) => void;
  variant?: "horizontal" | "vertical";
}

const getCategoryIcon = (categoryId: string) => {
  switch (categoryId) {
    case "home-services":
      return Home;
    case "personal-care":
      return Scissors;
    case "professional":
      return Briefcase;
    case "education":
      return GraduationCap;
    case "events":
      return Calendar;
    case "automotive":
      return Car;
    case "delivery":
      return Truck;
    case "tech-support":
      return Wrench;
    default:
      return Home;
  }
};

export default function CategoryCard({ category, onPress, variant = "horizontal" }: CategoryCardProps) {
  const { t } = useTranslation();
  const IconComponent = getCategoryIcon(category.id);

  return (
    <TouchableOpacity
      style={[styles.container, variant === "vertical" && styles.verticalContainer]}
      onPress={() => onPress(category)}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        <IconComponent size={32} color={colors.primary} />
      </View>
      <Text style={styles.categoryName}>{t(category.nameKey)}</Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 8,
    alignItems: "center",
    justifyContent: "center",
    minWidth: 100,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primaryLight,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 12,
  },
  categoryName: {
    ...typography.bodySmall,
    fontWeight: "500",
    textAlign: "center",
    color: colors.text,
  },
  verticalContainer: {
    marginVertical: 6,
    marginHorizontal: 0,
  },
});
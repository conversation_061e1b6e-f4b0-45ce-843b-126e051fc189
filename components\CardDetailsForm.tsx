import React, { useState } from "react";
import { StyleSheet, Text, View, TextInput, TouchableOpacity, Switch } from "react-native";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { Eye, EyeOff, CreditCard, Calendar, Lock } from "lucide-react-native";
import { useTranslation } from "@/hooks/useTranslation";

interface CardDetailsFormProps {
  onComplete: (isValid: boolean) => void;
  setAsDefault?: boolean;
  onSetDefaultChange?: (value: boolean) => void;
}

export default function CardDetailsForm({
  onComplete,
  setAsDefault = true,
  onSetDefaultChange
}: CardDetailsFormProps) {
  const { t } = useTranslation();

  const [cardNumber, setCardNumber] = useState("");
  const [cardholderName, setCardholderName] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [cvv, setCvv] = useState("");
  const [showCvv, setShowCvv] = useState(false);
  const [isDefaultCard, setIsDefaultCard] = useState(setAsDefault);

  const [errors, setErrors] = useState({
    cardNumber: "",
    cardholderName: "",
    expiryDate: "",
    cvv: "",
  });

  const formatCardNumber = (value: string) => {
    // Remove all non-digit characters
    const digits = value.replace(/\D/g, "");

    // Format with spaces every 4 digits
    let formatted = "";
    for (let i = 0; i < digits.length; i += 4) {
      formatted += digits.slice(i, i + 4) + " ";
    }

    return formatted.trim();
  };

  const formatExpiryDate = (value: string) => {
    // Remove all non-digit characters
    const digits = value.replace(/\D/g, "");

    // Format as MM/YY
    if (digits.length > 2) {
      return digits.slice(0, 2) + "/" + digits.slice(2, 4);
    }
    return digits;
  };

  const handleCardNumberChange = (value: string) => {
    const formatted = formatCardNumber(value);
    setCardNumber(formatted);

    // Validate and update errors
    if (formatted.replace(/\s/g, "").length < 16) {
      setErrors(prev => ({ ...prev, cardNumber: t("invalidCardNumber") }));
    } else {
      setErrors(prev => ({ ...prev, cardNumber: "" }));
    }

    validateForm();
  };

  const handleExpiryDateChange = (value: string) => {
    const formatted = formatExpiryDate(value);
    setExpiryDate(formatted);

    // Validate and update errors
    if (formatted.length < 5) {
      setErrors(prev => ({ ...prev, expiryDate: t("invalidExpiryDate") }));
    } else {
      setErrors(prev => ({ ...prev, expiryDate: "" }));
    }

    validateForm();
  };

  const handleCvvChange = (value: string) => {
    // Only allow up to 3 or 4 digits
    const digits = value.replace(/\D/g, "").slice(0, 4);
    setCvv(digits);

    // Validate and update errors
    if (digits.length < 3) {
      setErrors(prev => ({ ...prev, cvv: t("invalidCVV") }));
    } else {
      setErrors(prev => ({ ...prev, cvv: "" }));
    }

    validateForm();
  };

  const handleCardholderNameChange = (value: string) => {
    setCardholderName(value);

    // Validate and update errors
    if (value.trim().length < 3) {
      setErrors(prev => ({ ...prev, cardholderName: t("invalidName") }));
    } else {
      setErrors(prev => ({ ...prev, cardholderName: "" }));
    }

    validateForm();
  };

  const handleSetDefaultChange = (value: boolean) => {
    setIsDefaultCard(value);
    if (onSetDefaultChange) {
      onSetDefaultChange(value);
    }
  };

  const validateForm = () => {
    const isValid =
      cardNumber.replace(/\s/g, "").length >= 16 &&
      cardholderName.trim().length >= 3 &&
      expiryDate.length === 5 &&
      cvv.length >= 3;

    onComplete(isValid);
    return isValid;
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t("cardDetails")}</Text>

      <View style={styles.formGroup}>
        <Text style={styles.label}>{t("cardNumber")}</Text>
        <View style={[
          styles.inputContainer,
          errors.cardNumber ? styles.inputError : null
        ]}>
          <CreditCard size={20} color={colors.textSecondary} style={styles.inputIcon} />
          <TextInput
            style={styles.input}
            placeholder={t("cardNumberPlaceholder")}
            value={cardNumber}
            onChangeText={handleCardNumberChange}
            keyboardType="numeric"
            maxLength={19} // 16 digits + 3 spaces
          />
        </View>
        {errors.cardNumber ? <Text style={styles.errorText}>{errors.cardNumber}</Text> : null}
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>{t("cardholderName")}</Text>
        <TextInput
          style={[
            styles.input,
            errors.cardholderName ? styles.inputError : null
          ]}
          placeholder={t("enterCardholderName")}
          value={cardholderName}
          onChangeText={handleCardholderNameChange}
        />
        {errors.cardholderName ? <Text style={styles.errorText}>{errors.cardholderName}</Text> : null}
      </View>

      <View style={styles.row}>
        <View style={[styles.formGroup, styles.halfWidth]}>
          <Text style={styles.label}>{t("expiryDate")}</Text>
          <View style={[
            styles.inputContainer,
            errors.expiryDate ? styles.inputError : null
          ]}>
            <Calendar size={20} color={colors.textSecondary} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder={t("expiryDatePlaceholder")}
              value={expiryDate}
              onChangeText={handleExpiryDateChange}
              keyboardType="numeric"
              maxLength={5} // MM/YY
            />
          </View>
          {errors.expiryDate ? <Text style={styles.errorText}>{errors.expiryDate}</Text> : null}
        </View>

        <View style={[styles.formGroup, styles.halfWidth]}>
          <Text style={styles.label}>{t("cvv")}</Text>
          <View style={[
            styles.inputContainer,
            errors.cvv ? styles.inputError : null
          ]}>
            <Lock size={20} color={colors.textSecondary} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder={t("cvvPlaceholder")}
              value={cvv}
              onChangeText={handleCvvChange}
              keyboardType="numeric"
              maxLength={4}
              secureTextEntry={!showCvv}
            />
            <TouchableOpacity
              style={styles.eyeButton}
              onPress={() => setShowCvv(!showCvv)}
            >
              {showCvv ? (
                <EyeOff size={20} color={colors.textSecondary} />
              ) : (
                <Eye size={20} color={colors.textSecondary} />
              )}
            </TouchableOpacity>
          </View>
          {errors.cvv ? <Text style={styles.errorText}>{errors.cvv}</Text> : null}
        </View>
      </View>

      <View style={styles.defaultContainer}>
        <Text style={styles.defaultText}>{t("setAsDefaultPaymentMethod")}</Text>
        <Switch
          value={isDefaultCard}
          onValueChange={handleSetDefaultChange}
          trackColor={{ false: colors.border, true: colors.primary + "80" }}
          thumbColor={isDefaultCard ? colors.primary : colors.white}
        />
      </View>

      <View style={styles.secureInfo}>
        <Lock size={16} color={colors.textSecondary} />
        <Text style={styles.secureText}>
          {t("securePaymentNote")}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  title: {
    ...typography.h3,
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    ...typography.body,
    fontWeight: "500",
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  input: {
    flex: 1,
    paddingVertical: 12,
    ...typography.body,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  inputIcon: {
    marginRight: 8,
  },
  eyeButton: {
    padding: 8,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  errorText: {
    ...typography.caption,
    color: colors.error,
    marginTop: 4,
  },
  inputError: {
    borderColor: colors.error,
  },
  defaultContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginVertical: 16,
  },
  defaultText: {
    ...typography.body,
  },
  secureInfo: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
    gap: 8,
    backgroundColor: colors.primaryLight,
    padding: 12,
    borderRadius: 8,
  },
  secureText: {
    ...typography.caption,
    color: colors.primary,
    flex: 1,
  },
});
import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";

export interface Address {
  id: string;
  label: string;
  address: string;
  isDefault: boolean;
  type: "home" | "work" | "other";
}

interface AddressState {
  addresses: Address[];
  selectedAddress: Address | null;
  addAddress: (address: Omit<Address, "id">) => void;
  updateAddress: (id: string, address: Partial<Address>) => void;
  deleteAddress: (id: string) => void;
  setSelectedAddress: (address: Address | null) => void;
  setDefaultAddress: (id: string) => void;
  getDefaultAddress: () => Address | null;
}

export const useAddressStore = create<AddressState>()(
  persist(
    (set, get) => ({
      addresses: [],
      selectedAddress: null,

      addAddress: (address) => {
        const newAddress: Address = {
          ...address,
          id: Date.now().toString(),
        };

        set((state) => {
          // If this is the first address or marked as default, make it default
          const isFirstAddress = state.addresses.length === 0;
          const shouldBeDefault = address.isDefault || isFirstAddress;

          // If making this default, unset other defaults
          const updatedAddresses = shouldBeDefault
            ? state.addresses.map((addr) => ({ ...addr, isDefault: false }))
            : state.addresses;

          return {
            addresses: [...updatedAddresses, { ...newAddress, isDefault: shouldBeDefault }],
          };
        });
      },

      updateAddress: (id, updates) => {
        set((state) => ({
          addresses: state.addresses.map((addr) =>
            addr.id === id ? { ...addr, ...updates } : addr
          ),
        }));
      },

      deleteAddress: (id) => {
        set((state) => {
          const addressToDelete = state.addresses.find((addr) => addr.id === id);
          const remainingAddresses = state.addresses.filter((addr) => addr.id !== id);

          // If we deleted the default address, make the first remaining address default
          if (addressToDelete?.isDefault && remainingAddresses.length > 0) {
            remainingAddresses[0].isDefault = true;
          }

          return {
            addresses: remainingAddresses,
            selectedAddress: state.selectedAddress?.id === id ? null : state.selectedAddress,
          };
        });
      },

      setSelectedAddress: (address) => {
        set({ selectedAddress: address });
      },

      setDefaultAddress: (id) => {
        set((state) => ({
          addresses: state.addresses.map((addr) => ({
            ...addr,
            isDefault: addr.id === id,
          })),
        }));
      },

      getDefaultAddress: () => {
        const { addresses } = get();
        return addresses.find((addr) => addr.isDefault) || null;
      },
    }),
    {
      name: "address-storage",
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);

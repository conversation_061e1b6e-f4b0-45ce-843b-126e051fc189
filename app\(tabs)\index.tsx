import React, { useState } from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Image } from "react-native";
import { useRouter } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { serviceCategories } from "@/mocks/categories";
import { serviceProviders } from "@/mocks/providers";
import CategoryCard from "@/components/CategoryCard";
import ProviderCard from "@/components/ProviderCard";
import SearchBar from "@/components/SearchBar";
import { MapPin, Bell } from "lucide-react-native";
import { useAuthStore } from "@/stores/useAuthStore";
import { useTranslation } from "@/hooks/useTranslation";

export default function HomeScreen() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const { user, isAuthenticated } = useAuthStore();
  const { t } = useTranslation();

  // Get featured providers
  const featuredProviders = serviceProviders.filter(provider => provider.featured);

  // Get nearby providers (for demo, just use the first 3)
  const nearbyProviders = serviceProviders.slice(0, 3);

  const handleCategoryPress = (category: any) => {
    router.push({
      pathname: "/category/[id]",
      params: { id: category.id },
    });
  };

  const handleProviderPress = (provider: any) => {
    router.push({
      pathname: "/provider/[id]",
      params: { id: provider.id },
    });
  };

  const handleSearchSubmit = () => {
    if (searchQuery.trim()) {
      router.push({
        pathname: "/search",
        params: { query: searchQuery },
      });
    }
  };

  const handleLocationPress = () => {
    router.push("/profile/edit");
  };

  // Get first name safely with null check and default value
  const firstName = user?.name ? user.name.split(" ")[0] : "";

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.locationContainer}
          onPress={handleLocationPress}
        >
          <MapPin size={18} color={colors.primary} />
          <Text style={styles.location}>
            {user?.location ? t(user.location) : t("location.currentLocation")}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.notificationButton}>
          <Bell size={24} color={colors.text} />
          <View style={styles.notificationBadge} />
        </TouchableOpacity>
      </View>

      <View style={styles.welcomeSection}>
        <Text style={styles.welcomeText}>
          {isAuthenticated && firstName ? t("greeting.user", { name: firstName }) : t("greeting.guest")}
        </Text>
        <Text style={styles.tagline}>{t("findTrustedProviders")}</Text>
      </View>

      <View style={styles.searchContainer}>
        <SearchBar
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder={t("whatServiceDoYouNeed")}
          onSubmit={handleSearchSubmit}
          onClear={() => setSearchQuery("")}
        />
      </View>

      <View style={styles.categoriesSection}>
        <Text style={styles.sectionTitle}>{t("categories")}</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesContainer}
        >
          {serviceCategories.map((category) => (
            <CategoryCard
              key={category.id}
              category={category}
              onPress={handleCategoryPress}
            />
          ))}
          <TouchableOpacity
            style={styles.viewAllCard}
            onPress={() => router.push("/categories/all")}
            activeOpacity={0.7}
          >
            <View style={styles.viewAllIconContainer}>
              <Text style={styles.viewAllIcon}>📋</Text>
            </View>
            <Text style={styles.viewAllText}>{t("viewAll")}</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>

      <View style={styles.featuredSection}>
        <Text style={styles.sectionTitle}>{t("featuredProviders")}</Text>
        {featuredProviders.map((provider) => (
          <ProviderCard
            key={provider.id}
            provider={provider}
            onPress={handleProviderPress}
          />
        ))}
      </View>

      <View style={styles.nearbySection}>
        <Text style={styles.sectionTitle}>{t("nearbyProviders")}</Text>
        {nearbyProviders.map((provider) => (
          <ProviderCard
            key={provider.id}
            provider={provider}
            onPress={handleProviderPress}
          />
        ))}
      </View>

      <View style={styles.promoSection}>
        <Image
          source={{ uri: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80" }}
          style={styles.promoImage}
        />
        <View style={styles.promoContent}>
          <Text style={styles.promoTitle}>{t("needHelpingHand")}</Text>
          <Text style={styles.promoDescription}>
            {t("getDiscount")}
          </Text>
          <TouchableOpacity style={styles.promoButton}>
            <Text style={styles.promoButtonText}>{t("bookNow")}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  location: {
    ...typography.body,
    fontWeight: "500",
  },
  notificationButton: {
    position: "relative",
    padding: 4,
  },
  notificationBadge: {
    position: "absolute",
    top: 4,
    right: 4,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.error,
  },
  welcomeSection: {
    paddingHorizontal: 16,
    paddingTop: 24,
    paddingBottom: 16,
  },
  welcomeText: {
    ...typography.h2,
    marginBottom: 4,
    color: colors.primary,
  },
  tagline: {
    ...typography.body,
    color: colors.textSecondary,
  },
  searchContainer: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  categoriesSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    ...typography.h3,
    paddingHorizontal: 16,
    marginBottom: 16,
    color: colors.text,
  },
  categoriesContainer: {
    paddingHorizontal: 8,
    paddingBottom: 8,
  },
  viewAllCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 8,
    alignItems: "center",
    justifyContent: "center",
    minWidth: 100,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
    borderWidth: 2,
    borderColor: colors.primary,
    borderStyle: "dashed",
  },
  viewAllIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primaryLight,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 12,
  },
  viewAllIcon: {
    fontSize: 32,
  },
  viewAllText: {
    ...typography.bodySmall,
    fontWeight: "500",
    textAlign: "center",
    color: colors.primary,
  },
  featuredSection: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  nearbySection: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  promoSection: {
    margin: 16,
    borderRadius: 12,
    overflow: "hidden",
    backgroundColor: colors.white,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
    marginBottom: 32,
  },
  promoImage: {
    width: "100%",
    height: 150,
  },
  promoContent: {
    padding: 16,
  },
  promoTitle: {
    ...typography.h3,
    marginBottom: 8,
    color: colors.primary,
  },
  promoDescription: {
    ...typography.body,
    marginBottom: 16,
  },
  promoButton: {
    backgroundColor: colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignSelf: "flex-start",
  },
  promoButtonText: {
    ...typography.button,
    color: colors.white,
  },
});
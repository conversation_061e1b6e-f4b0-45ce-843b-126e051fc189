import React from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity } from "react-native";
import { Stack, useRouter } from "expo-router";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { Check } from "lucide-react-native";
import { useLanguageStore } from "@/stores/useLanguageStore";
import { useTranslation } from "@/hooks/useTranslation";

const languages = [
  { code: "en", name: "English" },
  { code: "zu", name: "isiZulu" },
  { code: "af", name: "Afrikaans" },
  { code: "xh", name: "isiXhosa" },
];

export default function LanguageScreen() {
  const router = useRouter();
  const { language, setLanguage } = useLanguageStore();
  const { t } = useTranslation();

  const handleLanguageChange = (languageCode: string) => {
    setLanguage(languageCode);
  };

  return (
    <>
      <Stack.Screen options={{ title: t("selectLanguage") }} />
      
      <View style={styles.container}>
        <Text style={styles.note}>{t("languageChangeNote")}</Text>
        
        <ScrollView style={styles.languageList}>
          {languages.map((lang) => (
            <TouchableOpacity
              key={lang.code}
              style={styles.languageItem}
              onPress={() => handleLanguageChange(lang.code)}
            >
              <Text style={styles.languageName}>{lang.name}</Text>
              {language === lang.code && (
                <Check size={20} color={colors.primary} />
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  note: {
    ...typography.body,
    color: colors.textSecondary,
    padding: 16,
    backgroundColor: colors.primaryLight,
  },
  languageList: {
    flex: 1,
  },
  languageItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  languageName: {
    ...typography.body,
  },
});
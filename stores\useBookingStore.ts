import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Service } from "@/mocks/providers";

export interface Booking {
  id: string;
  providerId: string;
  providerName: string;
  serviceId: string;
  serviceName: string;
  price: number;
  date: string;
  time: string;
  status: "pending" | "confirmed" | "completed" | "cancelled";
  notes?: string;
  createdAt: string;
}

interface BookingState {
  bookings: Booking[];
  currentBooking: {
    providerId: string;
    providerName: string;
    service: Service | null;
    date: string;
    time: string;
    notes: string;
  } | null;
  isLoading: boolean;
  error: string | null;

  // Current booking actions
  setCurrentBookingProvider: (providerId: string, providerName: string) => void;
  setCurrentBookingService: (service: Service, serviceName?: string) => void;
  setCurrentBookingDateTime: (date: string, time: string) => void;
  setCurrentBookingNotes: (notes: string) => void;
  clearCurrentBooking: () => void;

  // Booking management
  createBooking: () => Promise<Booking | null>;
  cancelBooking: (bookingId: string) => Promise<void>;
  updateBookingStatus: (bookingId: string, status: "pending" | "confirmed" | "completed" | "cancelled") => Promise<void>;
  getBookingById: (bookingId: string) => Booking | undefined;
  clearAllBookings: () => void;
}

export const useBookingStore = create<BookingState>()(
  persist(
    (set, get) => ({
      bookings: [],
      currentBooking: null,
      isLoading: false,
      error: null,

      setCurrentBookingProvider: (providerId, providerName) => {
        set((state) => ({
          currentBooking: {
            ...state.currentBooking || {
              service: null,
              date: "",
              time: "",
              notes: "",
            },
            providerId,
            providerName,
          },
        }));
      },

      setCurrentBookingService: (service, serviceName?: string) => {
        // Add the translated name to the service object if provided
        const serviceWithName = serviceName ? { ...service, name: serviceName } : service;

        set((state) => ({
          currentBooking: state.currentBooking ? {
            ...state.currentBooking,
            service: serviceWithName,
          } : {
            providerId: "",
            providerName: "",
            service: serviceWithName,
            date: "",
            time: "",
            notes: "",
          },
        }));
      },

      setCurrentBookingDateTime: (date, time) => {
        set((state) => ({
          currentBooking: state.currentBooking ? {
            ...state.currentBooking,
            date,
            time,
          } : null,
        }));
      },

      setCurrentBookingNotes: (notes) => {
        set((state) => ({
          currentBooking: state.currentBooking ? {
            ...state.currentBooking,
            notes,
          } : null,
        }));
      },

      clearCurrentBooking: () => {
        set({ currentBooking: null });
      },

      createBooking: async () => {
        const { currentBooking } = get();
        set({ isLoading: true, error: null });

        try {
          if (!currentBooking || !currentBooking.service) {
            throw new Error("Booking information is incomplete");
          }

          if (!currentBooking.date || !currentBooking.time) {
            throw new Error("Please select a date and time for your booking");
          }

          // Simulate API call
          await new Promise((resolve) => setTimeout(resolve, 1000));

          const newBooking: Booking = {
            id: `booking-${Date.now()}`,
            providerId: currentBooking.providerId,
            providerName: currentBooking.providerName,
            serviceId: currentBooking.service.id,
            serviceName: currentBooking.service.name || "",
            price: currentBooking.service.price,
            date: currentBooking.date,
            time: currentBooking.time,
            status: "pending",
            notes: currentBooking.notes,
            createdAt: new Date().toISOString(),
          };

          set((state) => ({
            bookings: [newBooking, ...state.bookings],
            currentBooking: null,
            isLoading: false,
          }));

          return newBooking;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : "Failed to create booking",
            isLoading: false,
          });
          return null;
        }
      },

      cancelBooking: async (bookingId) => {
        set({ isLoading: true, error: null });

        try {
          // Simulate API call
          await new Promise((resolve) => setTimeout(resolve, 1000));

          set((state) => ({
            bookings: state.bookings.map(booking =>
              booking.id === bookingId
                ? { ...booking, status: "cancelled" }
                : booking
            ),
            isLoading: false,
          }));
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : "Failed to cancel booking",
            isLoading: false,
          });
        }
      },

      updateBookingStatus: async (bookingId, status) => {
        set({ isLoading: true, error: null });

        try {
          // Simulate API call
          await new Promise((resolve) => setTimeout(resolve, 500));

          set((state) => ({
            bookings: state.bookings.map(booking =>
              booking.id === bookingId
                ? { ...booking, status }
                : booking
            ),
            isLoading: false,
          }));
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : "Failed to update booking status",
            isLoading: false,
          });
        }
      },

      getBookingById: (bookingId) => {
        return get().bookings.find(booking => booking.id === bookingId);
      },

      // Clear all bookings (useful for development/testing)
      clearAllBookings: () => {
        set({ bookings: [] });
      },
    }),
    {
      name: "booking-storage",
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        bookings: state.bookings,
        // Don't persist currentBooking to avoid stale data
      }),
    }
  )
);
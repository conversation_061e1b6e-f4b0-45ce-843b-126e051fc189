import React from "react";
import { StyleSheet, Text, View, TouchableOpacity, Image } from "react-native";
import { ServiceProvider } from "@/mocks/providers";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { Star, MapPin, CheckCircle } from "lucide-react-native";
import { useTranslation } from "@/hooks/useTranslation";

interface ProviderCardProps {
  provider: ServiceProvider;
  onPress: (provider: ServiceProvider) => void;
}

export default function ProviderCard({ provider, onPress }: ProviderCardProps) {
  const { t } = useTranslation();
  
  return (
    <TouchableOpacity
      style={styles.card}
      onPress={() => onPress(provider)}
      activeOpacity={0.7}
    >
      <View style={styles.header}>
        <Image source={{ uri: provider.avatar }} style={styles.avatar} />
        <View style={styles.headerInfo}>
          <View style={styles.nameContainer}>
            <Text style={styles.name} numberOfLines={1}>
              {t(provider.nameKey)}
            </Text>
            {provider.verified && (
              <CheckCircle size={16} color={colors.success} strokeWidth={2} />
            )}
          </View>
          <View style={styles.locationContainer}>
            <MapPin size={14} color={colors.textSecondary} strokeWidth={2} />
            <Text style={styles.location}>
              {t(provider.locationKey)} • {provider.distance} km
            </Text>
          </View>
        </View>
      </View>

      <Text style={styles.description} numberOfLines={2}>
        {t(provider.descriptionKey)}
      </Text>

      <View style={styles.footer}>
        <View style={styles.ratingContainer}>
          <Star
            size={16}
            color={colors.secondary}
            fill={colors.secondary}
            strokeWidth={2}
          />
          <Text style={styles.rating}>
            {provider.rating} ({provider.reviewCount})
          </Text>
        </View>
        <View style={styles.priceContainer}>
          <Text style={styles.priceLabel}>{t("from")}</Text>
          <Text style={styles.price}>
            R{Math.min(...provider.services.map((s) => s.price))}
          </Text>
        </View>
      </View>

      {provider.featured && <View style={styles.featuredBadge} />}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  header: {
    flexDirection: "row",
    marginBottom: 12,
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    marginRight: 12,
  },
  headerInfo: {
    flex: 1,
    justifyContent: "center",
  },
  nameContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  name: {
    ...typography.h4,
    flex: 1,
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 4,
    gap: 4,
  },
  location: {
    ...typography.caption,
  },
  description: {
    ...typography.bodySmall,
    marginBottom: 12,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  rating: {
    ...typography.bodySmall,
    fontWeight: "500",
  },
  priceContainer: {
    alignItems: "flex-end",
  },
  priceLabel: {
    ...typography.caption,
  },
  price: {
    ...typography.body,
    fontWeight: "600",
    color: colors.primary,
  },
  featuredBadge: {
    position: "absolute",
    top: 0,
    right: 0,
    width: 24,
    height: 24,
    backgroundColor: colors.secondary,
    borderBottomLeftRadius: 12,
    borderTopRightRadius: 12,
  },
});
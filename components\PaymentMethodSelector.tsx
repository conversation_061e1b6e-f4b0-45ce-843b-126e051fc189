import React from "react";
import { StyleSheet, Text, View, TouchableOpacity } from "react-native";
import colors from "@/constants/colors";
import typography from "@/constants/typography";
import { CreditCard, DollarSign, CheckCircle } from "lucide-react-native";

interface PaymentMethodSelectorProps {
  selectedMethod: "card" | "cash";
  onSelectMethod: (method: "card" | "cash") => void;
}

export default function PaymentMethodSelector({
  selectedMethod,
  onSelectMethod,
}: PaymentMethodSelectorProps) {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Select Payment Method</Text>
      
      <View style={styles.methodsContainer}>
        <TouchableOpacity
          style={[
            styles.methodCard,
            selectedMethod === "card" && styles.selectedMethodCard,
          ]}
          onPress={() => onSelectMethod("card")}
          activeOpacity={0.7}
        >
          <View style={styles.methodContent}>
            <View style={[styles.iconContainer, { backgroundColor: colors.primaryLight }]}>
              <CreditCard size={24} color={colors.primary} />
            </View>
            <View style={styles.methodInfo}>
              <Text style={styles.methodName}>Credit/Debit Card</Text>
              <Text style={styles.methodDescription}>Pay securely with your card</Text>
            </View>
          </View>
          {selectedMethod === "card" && (
            <CheckCircle size={20} color={colors.primary} fill={colors.primary} />
          )}
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.methodCard,
            selectedMethod === "cash" && styles.selectedMethodCard,
          ]}
          onPress={() => onSelectMethod("cash")}
          activeOpacity={0.7}
        >
          <View style={styles.methodContent}>
            <View style={[styles.iconContainer, { backgroundColor: colors.primaryLight }]}>
              <DollarSign size={24} color={colors.primary} />
            </View>
            <View style={styles.methodInfo}>
              <Text style={styles.methodName}>Cash</Text>
              <Text style={styles.methodDescription}>Pay directly to the service provider</Text>
            </View>
          </View>
          {selectedMethod === "cash" && (
            <CheckCircle size={20} color={colors.primary} fill={colors.primary} />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
  },
  title: {
    ...typography.h3,
    marginBottom: 16,
  },
  methodsContainer: {
    marginVertical: -6,
  },
  methodCard: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.white,
    marginVertical: 6,
  },
  selectedMethodCard: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight + "40", // 25% opacity
  },
  methodContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  methodInfo: {
    flex: 1,
  },
  methodName: {
    ...typography.body,
    fontWeight: "600",
    marginBottom: 4,
  },
  methodDescription: {
    ...typography.bodySmall,
    color: colors.textSecondary,
  },
});